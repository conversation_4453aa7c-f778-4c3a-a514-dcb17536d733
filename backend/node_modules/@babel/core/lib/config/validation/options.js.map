{"version": 3, "names": ["_removed", "require", "_optionAssertions", "_configError", "ROOT_VALIDATORS", "cwd", "assertString", "root", "rootMode", "assertRootMode", "configFile", "assertConfigFileSearch", "caller", "assertCallerMetadata", "filename", "filenameRelative", "code", "assertBoolean", "ast", "cloneInputAst", "envName", "BABELRC_VALIDATORS", "babelrc", "babelrcRoots", "assertBabelrcSearch", "NONPRESET_VALIDATORS", "extends", "ignore", "assertIgnoreList", "only", "targets", "assertTargets", "browserslistConfigFile", "browserslistEnv", "COMMON_VALIDATORS", "inputSourceMap", "assertInputSourceMap", "presets", "assertPluginList", "plugins", "passPerPreset", "assumptions", "assertAssumptions", "env", "assertEnvSet", "overrides", "assertOverridesList", "test", "assertConfigApplicableTest", "include", "exclude", "retainLines", "comments", "shouldPrintComment", "assertFunction", "compact", "assertCompact", "minified", "auxiliaryCommentBefore", "auxiliaryCommentAfter", "sourceType", "assertSourceType", "wrapPluginVisitorMethod", "highlightCode", "sourceMaps", "assertSourceMaps", "sourceMap", "sourceFileName", "sourceRoot", "parserOpts", "assertObject", "generatorOpts", "Object", "assign", "getModuleId", "moduleRoot", "moduleIds", "moduleId", "knownAssumptions", "assumptionsNames", "exports", "Set", "getSource", "loc", "type", "source", "parent", "validate", "opts", "validateNested", "error", "config<PERSON><PERSON>r", "ConfigError", "message", "assertNoDuplicateSourcemap", "keys", "for<PERSON>ach", "key", "optLoc", "name", "Error", "msg", "validator", "throwUnknownError", "removed", "version", "unknownOptErr", "hasOwnProperty", "call", "value", "obj", "access", "envLoc", "arr", "assertArray", "index", "item", "entries", "objLoc", "overridesLoc", "checkNoUnwrappedItemOptionPairs", "items", "e", "lastItem", "thisItem", "file", "options", "undefined", "request", "JSON", "stringify"], "sources": ["../../../src/config/validation/options.ts"], "sourcesContent": ["import type { InputTargets, Targets } from \"@babel/helper-compilation-targets\";\n\nimport type { ConfigItem } from \"../item.ts\";\nimport type Plugin from \"../plugin.ts\";\n\nimport removed from \"./removed.ts\";\nimport {\n  msg,\n  access,\n  assertString,\n  assertBoolean,\n  assertObject,\n  assertArray,\n  assertCallerMetadata,\n  assertInputSourceMap,\n  assertIgnoreList,\n  assertPluginList,\n  assertConfigApplicableTest,\n  assertConfigFileSearch,\n  assertBabelrcSearch,\n  assertFunction,\n  assertRootMode,\n  assertSourceMaps,\n  assertCompact,\n  assertSourceType,\n  assertTargets,\n  assertAssumptions,\n} from \"./option-assertions.ts\";\nimport type {\n  ValidatorSet,\n  Validator,\n  OptionPath,\n} from \"./option-assertions.ts\";\nimport type { UnloadedDescriptor } from \"../config-descriptors.ts\";\nimport type { PluginAPI } from \"../helpers/config-api.ts\";\nimport type { ParserOptions } from \"@babel/parser\";\nimport type { GeneratorOptions } from \"@babel/generator\";\nimport ConfigError from \"../../errors/config-error.ts\";\n\nconst ROOT_VALIDATORS: ValidatorSet = {\n  cwd: assertString as Validator<ValidatedOptions[\"cwd\"]>,\n  root: assertString as Validator<ValidatedOptions[\"root\"]>,\n  rootMode: assertRootMode as Validator<ValidatedOptions[\"rootMode\"]>,\n  configFile: assertConfigFileSearch as Validator<\n    ValidatedOptions[\"configFile\"]\n  >,\n\n  caller: assertCallerMetadata as Validator<ValidatedOptions[\"caller\"]>,\n  filename: assertString as Validator<ValidatedOptions[\"filename\"]>,\n  filenameRelative: assertString as Validator<\n    ValidatedOptions[\"filenameRelative\"]\n  >,\n  code: assertBoolean as Validator<ValidatedOptions[\"code\"]>,\n  ast: assertBoolean as Validator<ValidatedOptions[\"ast\"]>,\n\n  cloneInputAst: assertBoolean as Validator<ValidatedOptions[\"cloneInputAst\"]>,\n\n  envName: assertString as Validator<ValidatedOptions[\"envName\"]>,\n};\n\nconst BABELRC_VALIDATORS: ValidatorSet = {\n  babelrc: assertBoolean as Validator<ValidatedOptions[\"babelrc\"]>,\n  babelrcRoots: assertBabelrcSearch as Validator<\n    ValidatedOptions[\"babelrcRoots\"]\n  >,\n};\n\nconst NONPRESET_VALIDATORS: ValidatorSet = {\n  extends: assertString as Validator<ValidatedOptions[\"extends\"]>,\n  ignore: assertIgnoreList as Validator<ValidatedOptions[\"ignore\"]>,\n  only: assertIgnoreList as Validator<ValidatedOptions[\"only\"]>,\n\n  targets: assertTargets as Validator<ValidatedOptions[\"targets\"]>,\n  browserslistConfigFile: assertConfigFileSearch as Validator<\n    ValidatedOptions[\"browserslistConfigFile\"]\n  >,\n  browserslistEnv: assertString as Validator<\n    ValidatedOptions[\"browserslistEnv\"]\n  >,\n};\n\nconst COMMON_VALIDATORS: ValidatorSet = {\n  // TODO: Should 'inputSourceMap' be moved to be a root-only option?\n  // We may want a boolean-only version to be a common option, with the\n  // object only allowed as a root config argument.\n  inputSourceMap: assertInputSourceMap as Validator<\n    ValidatedOptions[\"inputSourceMap\"]\n  >,\n  presets: assertPluginList as Validator<ValidatedOptions[\"presets\"]>,\n  plugins: assertPluginList as Validator<ValidatedOptions[\"plugins\"]>,\n  passPerPreset: assertBoolean as Validator<ValidatedOptions[\"passPerPreset\"]>,\n  assumptions: assertAssumptions as Validator<ValidatedOptions[\"assumptions\"]>,\n\n  env: assertEnvSet as Validator<ValidatedOptions[\"env\"]>,\n  overrides: assertOverridesList as Validator<ValidatedOptions[\"overrides\"]>,\n\n  // We could limit these to 'overrides' blocks, but it's not clear why we'd\n  // bother, when the ability to limit a config to a specific set of files\n  // is a fairly general useful feature.\n  test: assertConfigApplicableTest as Validator<ValidatedOptions[\"test\"]>,\n  include: assertConfigApplicableTest as Validator<ValidatedOptions[\"include\"]>,\n  exclude: assertConfigApplicableTest as Validator<ValidatedOptions[\"exclude\"]>,\n\n  retainLines: assertBoolean as Validator<ValidatedOptions[\"retainLines\"]>,\n  comments: assertBoolean as Validator<ValidatedOptions[\"comments\"]>,\n  shouldPrintComment: assertFunction as Validator<\n    ValidatedOptions[\"shouldPrintComment\"]\n  >,\n  compact: assertCompact as Validator<ValidatedOptions[\"compact\"]>,\n  minified: assertBoolean as Validator<ValidatedOptions[\"minified\"]>,\n  auxiliaryCommentBefore: assertString as Validator<\n    ValidatedOptions[\"auxiliaryCommentBefore\"]\n  >,\n  auxiliaryCommentAfter: assertString as Validator<\n    ValidatedOptions[\"auxiliaryCommentAfter\"]\n  >,\n  sourceType: assertSourceType as Validator<ValidatedOptions[\"sourceType\"]>,\n  wrapPluginVisitorMethod: assertFunction as Validator<\n    ValidatedOptions[\"wrapPluginVisitorMethod\"]\n  >,\n  highlightCode: assertBoolean as Validator<ValidatedOptions[\"highlightCode\"]>,\n  sourceMaps: assertSourceMaps as Validator<ValidatedOptions[\"sourceMaps\"]>,\n  sourceMap: assertSourceMaps as Validator<ValidatedOptions[\"sourceMap\"]>,\n  sourceFileName: assertString as Validator<ValidatedOptions[\"sourceFileName\"]>,\n  sourceRoot: assertString as Validator<ValidatedOptions[\"sourceRoot\"]>,\n  parserOpts: assertObject as Validator<ValidatedOptions[\"parserOpts\"]>,\n  generatorOpts: assertObject as Validator<ValidatedOptions[\"generatorOpts\"]>,\n};\nif (!process.env.BABEL_8_BREAKING) {\n  Object.assign(COMMON_VALIDATORS, {\n    getModuleId: assertFunction,\n    moduleRoot: assertString,\n    moduleIds: assertBoolean,\n    moduleId: assertString,\n  });\n}\n\nexport type InputOptions = ValidatedOptions;\n\nexport type ValidatedOptions = {\n  cwd?: string;\n  filename?: string;\n  filenameRelative?: string;\n  babelrc?: boolean;\n  babelrcRoots?: BabelrcSearch;\n  configFile?: ConfigFileSearch;\n  root?: string;\n  rootMode?: RootMode;\n  code?: boolean;\n  ast?: boolean;\n  cloneInputAst?: boolean;\n  inputSourceMap?: RootInputSourceMapOption;\n  envName?: string;\n  caller?: CallerMetadata;\n  extends?: string;\n  env?: EnvSet<ValidatedOptions>;\n  ignore?: IgnoreList;\n  only?: IgnoreList;\n  overrides?: OverridesList;\n  showIgnoredFiles?: boolean;\n  // Generally verify if a given config object should be applied to the given file.\n  test?: ConfigApplicableTest;\n  include?: ConfigApplicableTest;\n  exclude?: ConfigApplicableTest;\n  presets?: PluginList;\n  plugins?: PluginList;\n  passPerPreset?: boolean;\n  assumptions?: {\n    [name: string]: boolean;\n  };\n  // browserslists-related options\n  targets?: TargetsListOrObject;\n  browserslistConfigFile?: ConfigFileSearch;\n  browserslistEnv?: string;\n  // Options for @babel/generator\n  retainLines?: boolean;\n  comments?: boolean;\n  shouldPrintComment?: Function;\n  compact?: CompactOption;\n  minified?: boolean;\n  auxiliaryCommentBefore?: string;\n  auxiliaryCommentAfter?: string;\n  // Parser\n  sourceType?: SourceTypeOption;\n  wrapPluginVisitorMethod?: Function;\n  highlightCode?: boolean;\n  // Sourcemap generation options.\n  sourceMaps?: SourceMapsOption;\n  sourceMap?: SourceMapsOption;\n  sourceFileName?: string;\n  sourceRoot?: string;\n  // Deprecate top level parserOpts\n  parserOpts?: ParserOptions;\n  // Deprecate top level generatorOpts\n  generatorOpts?: GeneratorOptions;\n};\n\nexport type NormalizedOptions = {\n  readonly targets: Targets;\n} & Omit<ValidatedOptions, \"targets\">;\n\nexport type CallerMetadata = {\n  // If 'caller' is specified, require that the name is given for debugging\n  // messages.\n  name: string;\n};\nexport type EnvSet<T> = {\n  [x: string]: T;\n};\nexport type IgnoreItem =\n  | string\n  | RegExp\n  | ((\n      path: string | undefined,\n      context: { dirname: string; caller: CallerMetadata; envName: string },\n    ) => unknown);\nexport type IgnoreList = ReadonlyArray<IgnoreItem>;\n\nexport type PluginOptions = object | void | false;\nexport type PluginTarget = string | object | Function;\nexport type PluginItem =\n  | ConfigItem<PluginAPI>\n  | Plugin\n  | PluginTarget\n  | [PluginTarget, PluginOptions]\n  | [PluginTarget, PluginOptions, string | void];\nexport type PluginList = ReadonlyArray<PluginItem>;\n\nexport type OverridesList = Array<ValidatedOptions>;\nexport type ConfigApplicableTest = IgnoreItem | Array<IgnoreItem>;\n\nexport type ConfigFileSearch = string | boolean;\nexport type BabelrcSearch = boolean | IgnoreItem | IgnoreList;\nexport type SourceMapsOption = boolean | \"inline\" | \"both\";\nexport type SourceTypeOption = \"module\" | \"script\" | \"unambiguous\";\nexport type CompactOption = boolean | \"auto\";\nexport type RootInputSourceMapOption = object | boolean;\nexport type RootMode = \"root\" | \"upward\" | \"upward-optional\";\n\nexport type TargetsListOrObject =\n  | Targets\n  | InputTargets\n  | InputTargets[\"browsers\"];\n\nexport type OptionsSource =\n  | \"arguments\"\n  | \"configfile\"\n  | \"babelrcfile\"\n  | \"extendsfile\"\n  | \"preset\"\n  | \"plugin\";\n\nexport type RootPath = Readonly<{\n  type: \"root\";\n  source: OptionsSource;\n}>;\n\ntype OverridesPath = Readonly<{\n  type: \"overrides\";\n  index: number;\n  parent: RootPath;\n}>;\n\ntype EnvPath = Readonly<{\n  type: \"env\";\n  name: string;\n  parent: RootPath | OverridesPath;\n}>;\n\nexport type NestingPath = RootPath | OverridesPath | EnvPath;\n\nconst knownAssumptions = [\n  \"arrayLikeIsIterable\",\n  \"constantReexports\",\n  \"constantSuper\",\n  \"enumerableModuleMeta\",\n  \"ignoreFunctionLength\",\n  \"ignoreToPrimitiveHint\",\n  \"iterableIsArray\",\n  \"mutableTemplateObject\",\n  \"noClassCalls\",\n  \"noDocumentAll\",\n  \"noIncompleteNsImportDetection\",\n  \"noNewArrows\",\n  \"noUninitializedPrivateFieldAccess\",\n  \"objectRestNoSymbols\",\n  \"privateFieldsAsSymbols\",\n  \"privateFieldsAsProperties\",\n  \"pureGetters\",\n  \"setClassMethods\",\n  \"setComputedProperties\",\n  \"setPublicClassFields\",\n  \"setSpreadProperties\",\n  \"skipForOfIteratorClosing\",\n  \"superIsCallableConstructor\",\n] as const;\nexport type AssumptionName = (typeof knownAssumptions)[number];\nexport const assumptionsNames = new Set(knownAssumptions);\n\nfunction getSource(loc: NestingPath): OptionsSource {\n  return loc.type === \"root\" ? loc.source : getSource(loc.parent);\n}\n\nexport function validate(\n  type: OptionsSource,\n  opts: any,\n  filename?: string,\n): ValidatedOptions {\n  try {\n    return validateNested(\n      {\n        type: \"root\",\n        source: type,\n      },\n      opts,\n    );\n  } catch (error) {\n    const configError = new ConfigError(error.message, filename);\n    // @ts-expect-error TODO: .code is not defined on ConfigError or Error\n    if (error.code) configError.code = error.code;\n    throw configError;\n  }\n}\n\nfunction validateNested(loc: NestingPath, opts: { [key: string]: unknown }) {\n  const type = getSource(loc);\n\n  assertNoDuplicateSourcemap(opts);\n\n  Object.keys(opts).forEach((key: string) => {\n    const optLoc = {\n      type: \"option\",\n      name: key,\n      parent: loc,\n    } as const;\n\n    if (type === \"preset\" && NONPRESET_VALIDATORS[key]) {\n      throw new Error(`${msg(optLoc)} is not allowed in preset options`);\n    }\n    if (type !== \"arguments\" && ROOT_VALIDATORS[key]) {\n      throw new Error(\n        `${msg(optLoc)} is only allowed in root programmatic options`,\n      );\n    }\n    if (\n      type !== \"arguments\" &&\n      type !== \"configfile\" &&\n      BABELRC_VALIDATORS[key]\n    ) {\n      if (type === \"babelrcfile\" || type === \"extendsfile\") {\n        throw new Error(\n          `${msg(\n            optLoc,\n          )} is not allowed in .babelrc or \"extends\"ed files, only in root programmatic options, ` +\n            `or babel.config.js/config file options`,\n        );\n      }\n\n      throw new Error(\n        `${msg(\n          optLoc,\n        )} is only allowed in root programmatic options, or babel.config.js/config file options`,\n      );\n    }\n\n    const validator =\n      COMMON_VALIDATORS[key] ||\n      NONPRESET_VALIDATORS[key] ||\n      BABELRC_VALIDATORS[key] ||\n      ROOT_VALIDATORS[key] ||\n      (throwUnknownError as Validator<void>);\n\n    validator(optLoc, opts[key]);\n  });\n\n  return opts;\n}\n\nfunction throwUnknownError(loc: OptionPath) {\n  const key = loc.name;\n\n  if (removed[key]) {\n    const { message, version = 5 } = removed[key];\n\n    throw new Error(\n      `Using removed Babel ${version} option: ${msg(loc)} - ${message}`,\n    );\n  } else {\n    const unknownOptErr = new Error(\n      `Unknown option: ${msg(\n        loc,\n      )}. Check out https://babeljs.io/docs/en/babel-core/#options for more information about options.`,\n    );\n    // @ts-expect-error todo(flow->ts): consider creating something like BabelConfigError with code field in it\n    unknownOptErr.code = \"BABEL_UNKNOWN_OPTION\";\n\n    throw unknownOptErr;\n  }\n}\n\nfunction assertNoDuplicateSourcemap(opts: any): void {\n  if (Object.hasOwn(opts, \"sourceMap\") && Object.hasOwn(opts, \"sourceMaps\")) {\n    throw new Error(\".sourceMap is an alias for .sourceMaps, cannot use both\");\n  }\n}\n\nfunction assertEnvSet(\n  loc: OptionPath,\n  value: unknown,\n): void | EnvSet<ValidatedOptions> {\n  if (loc.parent.type === \"env\") {\n    throw new Error(`${msg(loc)} is not allowed inside of another .env block`);\n  }\n  const parent: RootPath | OverridesPath = loc.parent;\n\n  const obj = assertObject(loc, value);\n  if (obj) {\n    // Validate but don't copy the .env object in order to preserve\n    // object identity for use during config chain processing.\n    for (const envName of Object.keys(obj)) {\n      const env = assertObject(access(loc, envName), obj[envName]);\n      if (!env) continue;\n\n      const envLoc = {\n        type: \"env\",\n        name: envName,\n        parent,\n      } as const;\n      validateNested(envLoc, env);\n    }\n  }\n  return obj;\n}\n\nfunction assertOverridesList(\n  loc: OptionPath,\n  value: unknown[],\n): undefined | OverridesList {\n  if (loc.parent.type === \"env\") {\n    throw new Error(`${msg(loc)} is not allowed inside an .env block`);\n  }\n  if (loc.parent.type === \"overrides\") {\n    throw new Error(`${msg(loc)} is not allowed inside an .overrides block`);\n  }\n  const parent: RootPath = loc.parent;\n\n  const arr = assertArray(loc, value);\n  if (arr) {\n    for (const [index, item] of arr.entries()) {\n      const objLoc = access(loc, index);\n      const env = assertObject(objLoc, item);\n      if (!env) throw new Error(`${msg(objLoc)} must be an object`);\n\n      const overridesLoc = {\n        type: \"overrides\",\n        index,\n        parent,\n      } as const;\n      validateNested(overridesLoc, env);\n    }\n  }\n  return arr as OverridesList;\n}\n\nexport function checkNoUnwrappedItemOptionPairs<API>(\n  items: Array<UnloadedDescriptor<API>>,\n  index: number,\n  type: \"plugin\" | \"preset\",\n  e: Error,\n): void {\n  if (index === 0) return;\n\n  const lastItem = items[index - 1];\n  const thisItem = items[index];\n\n  if (\n    lastItem.file &&\n    lastItem.options === undefined &&\n    typeof thisItem.value === \"object\"\n  ) {\n    e.message +=\n      `\\n- Maybe you meant to use\\n` +\n      `\"${type}s\": [\\n  [\"${lastItem.file.request}\", ${JSON.stringify(\n        thisItem.value,\n        undefined,\n        2,\n      )}]\\n]\\n` +\n      `To be a valid ${type}, its name and options should be wrapped in a pair of brackets`;\n  }\n}\n"], "mappings": ";;;;;;;;AAKA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,iBAAA,GAAAD,OAAA;AA+BA,IAAAE,YAAA,GAAAF,OAAA;AAEA,MAAMG,eAA6B,GAAG;EACpCC,GAAG,EAAEC,8BAAkD;EACvDC,IAAI,EAAED,8BAAmD;EACzDE,QAAQ,EAAEC,gCAAyD;EACnEC,UAAU,EAAEC,wCAEX;EAEDC,MAAM,EAAEC,sCAA6D;EACrEC,QAAQ,EAAER,8BAAuD;EACjES,gBAAgB,EAAET,8BAEjB;EACDU,IAAI,EAAEC,+BAAoD;EAC1DC,GAAG,EAAED,+BAAmD;EAExDE,aAAa,EAAEF,+BAA6D;EAE5EG,OAAO,EAAEd;AACX,CAAC;AAED,MAAMe,kBAAgC,GAAG;EACvCC,OAAO,EAAEL,+BAAuD;EAChEM,YAAY,EAAEC;AAGhB,CAAC;AAED,MAAMC,oBAAkC,GAAG;EACzCC,OAAO,EAAEpB,8BAAsD;EAC/DqB,MAAM,EAAEC,kCAAyD;EACjEC,IAAI,EAAED,kCAAuD;EAE7DE,OAAO,EAAEC,+BAAuD;EAChEC,sBAAsB,EAAErB,wCAEvB;EACDsB,eAAe,EAAE3B;AAGnB,CAAC;AAED,MAAM4B,iBAA+B,GAAG;EAItCC,cAAc,EAAEC,sCAEf;EACDC,OAAO,EAAEC,kCAA0D;EACnEC,OAAO,EAAED,kCAA0D;EACnEE,aAAa,EAAEvB,+BAA6D;EAC5EwB,WAAW,EAAEC,mCAA+D;EAE5EC,GAAG,EAAEC,YAAkD;EACvDC,SAAS,EAAEC,mBAA+D;EAK1EC,IAAI,EAAEC,4CAAiE;EACvEC,OAAO,EAAED,4CAAoE;EAC7EE,OAAO,EAAEF,4CAAoE;EAE7EG,WAAW,EAAElC,+BAA2D;EACxEmC,QAAQ,EAAEnC,+BAAwD;EAClEoC,kBAAkB,EAAEC,gCAEnB;EACDC,OAAO,EAAEC,+BAAuD;EAChEC,QAAQ,EAAExC,+BAAwD;EAClEyC,sBAAsB,EAAEpD,8BAEvB;EACDqD,qBAAqB,EAAErD,8BAEtB;EACDsD,UAAU,EAAEC,kCAA6D;EACzEC,uBAAuB,EAAER,gCAExB;EACDS,aAAa,EAAE9C,+BAA6D;EAC5E+C,UAAU,EAAEC,kCAA6D;EACzEC,SAAS,EAAED,kCAA4D;EACvEE,cAAc,EAAE7D,8BAA6D;EAC7E8D,UAAU,EAAE9D,8BAAyD;EACrE+D,UAAU,EAAEC,8BAAyD;EACrEC,aAAa,EAAED;AACjB,CAAC;AACkC;EACjCE,MAAM,CAACC,MAAM,CAACvC,iBAAiB,EAAE;IAC/BwC,WAAW,EAAEpB,gCAAc;IAC3BqB,UAAU,EAAErE,8BAAY;IACxBsE,SAAS,EAAE3D,+BAAa;IACxB4D,QAAQ,EAAEvE;EACZ,CAAC,CAAC;AACJ;AAwIA,MAAMwE,gBAAgB,GAAG,CACvB,qBAAqB,EACrB,mBAAmB,EACnB,eAAe,EACf,sBAAsB,EACtB,sBAAsB,EACtB,uBAAuB,EACvB,iBAAiB,EACjB,uBAAuB,EACvB,cAAc,EACd,eAAe,EACf,+BAA+B,EAC/B,aAAa,EACb,mCAAmC,EACnC,qBAAqB,EACrB,wBAAwB,EACxB,2BAA2B,EAC3B,aAAa,EACb,iBAAiB,EACjB,uBAAuB,EACvB,sBAAsB,EACtB,qBAAqB,EACrB,0BAA0B,EAC1B,4BAA4B,CACpB;AAEH,MAAMC,gBAAgB,GAAAC,OAAA,CAAAD,gBAAA,GAAG,IAAIE,GAAG,CAACH,gBAAgB,CAAC;AAEzD,SAASI,SAASA,CAACC,GAAgB,EAAiB;EAClD,OAAOA,GAAG,CAACC,IAAI,KAAK,MAAM,GAAGD,GAAG,CAACE,MAAM,GAAGH,SAAS,CAACC,GAAG,CAACG,MAAM,CAAC;AACjE;AAEO,SAASC,QAAQA,CACtBH,IAAmB,EACnBI,IAAS,EACT1E,QAAiB,EACC;EAClB,IAAI;IACF,OAAO2E,cAAc,CACnB;MACEL,IAAI,EAAE,MAAM;MACZC,MAAM,EAAED;IACV,CAAC,EACDI,IACF,CAAC;EACH,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd,MAAMC,WAAW,GAAG,IAAIC,oBAAW,CAACF,KAAK,CAACG,OAAO,EAAE/E,QAAQ,CAAC;IAE5D,IAAI4E,KAAK,CAAC1E,IAAI,EAAE2E,WAAW,CAAC3E,IAAI,GAAG0E,KAAK,CAAC1E,IAAI;IAC7C,MAAM2E,WAAW;EACnB;AACF;AAEA,SAASF,cAAcA,CAACN,GAAgB,EAAEK,IAAgC,EAAE;EAC1E,MAAMJ,IAAI,GAAGF,SAAS,CAACC,GAAG,CAAC;EAE3BW,0BAA0B,CAACN,IAAI,CAAC;EAEhChB,MAAM,CAACuB,IAAI,CAACP,IAAI,CAAC,CAACQ,OAAO,CAAEC,GAAW,IAAK;IACzC,MAAMC,MAAM,GAAG;MACbd,IAAI,EAAE,QAAQ;MACde,IAAI,EAAEF,GAAG;MACTX,MAAM,EAAEH;IACV,CAAU;IAEV,IAAIC,IAAI,KAAK,QAAQ,IAAI3D,oBAAoB,CAACwE,GAAG,CAAC,EAAE;MAClD,MAAM,IAAIG,KAAK,CAAC,GAAG,IAAAC,qBAAG,EAACH,MAAM,CAAC,mCAAmC,CAAC;IACpE;IACA,IAAId,IAAI,KAAK,WAAW,IAAIhF,eAAe,CAAC6F,GAAG,CAAC,EAAE;MAChD,MAAM,IAAIG,KAAK,CACb,GAAG,IAAAC,qBAAG,EAACH,MAAM,CAAC,+CAChB,CAAC;IACH;IACA,IACEd,IAAI,KAAK,WAAW,IACpBA,IAAI,KAAK,YAAY,IACrB/D,kBAAkB,CAAC4E,GAAG,CAAC,EACvB;MACA,IAAIb,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,aAAa,EAAE;QACpD,MAAM,IAAIgB,KAAK,CACb,GAAG,IAAAC,qBAAG,EACJH,MACF,CAAC,uFAAuF,GACtF,wCACJ,CAAC;MACH;MAEA,MAAM,IAAIE,KAAK,CACb,GAAG,IAAAC,qBAAG,EACJH,MACF,CAAC,uFACH,CAAC;IACH;IAEA,MAAMI,SAAS,GACbpE,iBAAiB,CAAC+D,GAAG,CAAC,IACtBxE,oBAAoB,CAACwE,GAAG,CAAC,IACzB5E,kBAAkB,CAAC4E,GAAG,CAAC,IACvB7F,eAAe,CAAC6F,GAAG,CAAC,IACnBM,iBAAqC;IAExCD,SAAS,CAACJ,MAAM,EAAEV,IAAI,CAACS,GAAG,CAAC,CAAC;EAC9B,CAAC,CAAC;EAEF,OAAOT,IAAI;AACb;AAEA,SAASe,iBAAiBA,CAACpB,GAAe,EAAE;EAC1C,MAAMc,GAAG,GAAGd,GAAG,CAACgB,IAAI;EAEpB,IAAIK,gBAAO,CAACP,GAAG,CAAC,EAAE;IAChB,MAAM;MAAEJ,OAAO;MAAEY,OAAO,GAAG;IAAE,CAAC,GAAGD,gBAAO,CAACP,GAAG,CAAC;IAE7C,MAAM,IAAIG,KAAK,CACb,uBAAuBK,OAAO,YAAY,IAAAJ,qBAAG,EAAClB,GAAG,CAAC,MAAMU,OAAO,EACjE,CAAC;EACH,CAAC,MAAM;IACL,MAAMa,aAAa,GAAG,IAAIN,KAAK,CAC7B,mBAAmB,IAAAC,qBAAG,EACpBlB,GACF,CAAC,gGACH,CAAC;IAEDuB,aAAa,CAAC1F,IAAI,GAAG,sBAAsB;IAE3C,MAAM0F,aAAa;EACrB;AACF;AAEA,SAASZ,0BAA0BA,CAACN,IAAS,EAAQ;EACnD,IAAImB,cAAA,CAAAC,IAAA,CAAcpB,IAAI,EAAE,WAAW,CAAC,IAAImB,cAAA,CAAAC,IAAA,CAAcpB,IAAI,EAAE,YAAY,CAAC,EAAE;IACzE,MAAM,IAAIY,KAAK,CAAC,yDAAyD,CAAC;EAC5E;AACF;AAEA,SAASxD,YAAYA,CACnBuC,GAAe,EACf0B,KAAc,EACmB;EACjC,IAAI1B,GAAG,CAACG,MAAM,CAACF,IAAI,KAAK,KAAK,EAAE;IAC7B,MAAM,IAAIgB,KAAK,CAAC,GAAG,IAAAC,qBAAG,EAAClB,GAAG,CAAC,8CAA8C,CAAC;EAC5E;EACA,MAAMG,MAAgC,GAAGH,GAAG,CAACG,MAAM;EAEnD,MAAMwB,GAAG,GAAG,IAAAxC,8BAAY,EAACa,GAAG,EAAE0B,KAAK,CAAC;EACpC,IAAIC,GAAG,EAAE;IAGP,KAAK,MAAM1F,OAAO,IAAIoD,MAAM,CAACuB,IAAI,CAACe,GAAG,CAAC,EAAE;MACtC,MAAMnE,GAAG,GAAG,IAAA2B,8BAAY,EAAC,IAAAyC,wBAAM,EAAC5B,GAAG,EAAE/D,OAAO,CAAC,EAAE0F,GAAG,CAAC1F,OAAO,CAAC,CAAC;MAC5D,IAAI,CAACuB,GAAG,EAAE;MAEV,MAAMqE,MAAM,GAAG;QACb5B,IAAI,EAAE,KAAK;QACXe,IAAI,EAAE/E,OAAO;QACbkE;MACF,CAAU;MACVG,cAAc,CAACuB,MAAM,EAAErE,GAAG,CAAC;IAC7B;EACF;EACA,OAAOmE,GAAG;AACZ;AAEA,SAAShE,mBAAmBA,CAC1BqC,GAAe,EACf0B,KAAgB,EACW;EAC3B,IAAI1B,GAAG,CAACG,MAAM,CAACF,IAAI,KAAK,KAAK,EAAE;IAC7B,MAAM,IAAIgB,KAAK,CAAC,GAAG,IAAAC,qBAAG,EAAClB,GAAG,CAAC,sCAAsC,CAAC;EACpE;EACA,IAAIA,GAAG,CAACG,MAAM,CAACF,IAAI,KAAK,WAAW,EAAE;IACnC,MAAM,IAAIgB,KAAK,CAAC,GAAG,IAAAC,qBAAG,EAAClB,GAAG,CAAC,4CAA4C,CAAC;EAC1E;EACA,MAAMG,MAAgB,GAAGH,GAAG,CAACG,MAAM;EAEnC,MAAM2B,GAAG,GAAG,IAAAC,6BAAW,EAAC/B,GAAG,EAAE0B,KAAK,CAAC;EACnC,IAAII,GAAG,EAAE;IACP,KAAK,MAAM,CAACE,KAAK,EAAEC,IAAI,CAAC,IAAIH,GAAG,CAACI,OAAO,CAAC,CAAC,EAAE;MACzC,MAAMC,MAAM,GAAG,IAAAP,wBAAM,EAAC5B,GAAG,EAAEgC,KAAK,CAAC;MACjC,MAAMxE,GAAG,GAAG,IAAA2B,8BAAY,EAACgD,MAAM,EAAEF,IAAI,CAAC;MACtC,IAAI,CAACzE,GAAG,EAAE,MAAM,IAAIyD,KAAK,CAAC,GAAG,IAAAC,qBAAG,EAACiB,MAAM,CAAC,oBAAoB,CAAC;MAE7D,MAAMC,YAAY,GAAG;QACnBnC,IAAI,EAAE,WAAW;QACjB+B,KAAK;QACL7B;MACF,CAAU;MACVG,cAAc,CAAC8B,YAAY,EAAE5E,GAAG,CAAC;IACnC;EACF;EACA,OAAOsE,GAAG;AACZ;AAEO,SAASO,+BAA+BA,CAC7CC,KAAqC,EACrCN,KAAa,EACb/B,IAAyB,EACzBsC,CAAQ,EACF;EACN,IAAIP,KAAK,KAAK,CAAC,EAAE;EAEjB,MAAMQ,QAAQ,GAAGF,KAAK,CAACN,KAAK,GAAG,CAAC,CAAC;EACjC,MAAMS,QAAQ,GAAGH,KAAK,CAACN,KAAK,CAAC;EAE7B,IACEQ,QAAQ,CAACE,IAAI,IACbF,QAAQ,CAACG,OAAO,KAAKC,SAAS,IAC9B,OAAOH,QAAQ,CAACf,KAAK,KAAK,QAAQ,EAClC;IACAa,CAAC,CAAC7B,OAAO,IACP,8BAA8B,GAC9B,IAAIT,IAAI,cAAcuC,QAAQ,CAACE,IAAI,CAACG,OAAO,MAAMC,IAAI,CAACC,SAAS,CAC7DN,QAAQ,CAACf,KAAK,EACdkB,SAAS,EACT,CACF,CAAC,QAAQ,GACT,iBAAiB3C,IAAI,gEAAgE;EACzF;AACF;AAAC", "ignoreList": []}